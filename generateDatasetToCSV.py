import csv
import random
from datetime import datetime, timedelta
from generateDatasets import (
    get_business_type,
    business_addresses,
    business_digital_info,
)

import pandas as pd
from pmdarima import auto_arima
import matplotlib.pyplot as plt


# List of business names
business_names = list(business_addresses.keys())

# Helper to generate random operating hours
operating_hours_options = [
    "08:00-16:00", "09:00-17:00", "10:00-18:00", "11:00-19:00", "24 Jam"
]

def random_bool(prob_true=0.5):
    return random.random() < prob_true

def generate_dataset_csv(filename="synthetic_store_data.csv", n_stores=20, start_year=2020, end_year=2025):
    # Prepare date range
    start_date = datetime(start_year, 1, 1)
    end_date = datetime(end_year, 12, 31)
    delta = end_date - start_date
    all_dates = [start_date + timedelta(days=i) for i in range(delta.days + 1)]

    # Randomly select stores
    selected_stores = random.sample(business_names, n_stores)

    # Prepare CSV columns
    columns = [
        "no", "businessName", "businessType", "address", "isDigital", "hasWebsite",
        "promo", "nationalHoliday", "schoolHoliday", "isWeekend", "dayofWeek", "isOpen", "operatingHour",
        "date", "sales", "customers"
    ]

    with open(filename, mode="w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=columns)
        writer.writeheader()

        store_id = 1
        for business_name in selected_stores:
            business_type = get_business_type(business_name)
            address = business_addresses.get(business_name, "Unknown")
            is_digital, has_website = business_digital_info.get(business_name, (False, False))
            operating_hour = random.choice(operating_hours_options)

            # Simulate for a random subset of days (e.g., 100-200 days per store)
            n_days = random.randint(100, 200)
            store_dates = random.sample(all_dates, n_days)
            store_dates.sort()

            for date_obj in store_dates:
                dayofweek = date_obj.weekday()
                is_weekend = dayofweek >= 5
                is_open = random_bool(0.95 if not is_weekend else 0.8)
                promo = random_bool(0.2)
                national_holiday = random_bool(0.05)
                school_holiday = random_bool(0.1)
                sales = round(random.uniform(500_000, 10_000_000) * (1.2 if promo else 1.0) * (1.1 if is_open else 0.5), 2)
                customers = int(random.gauss(50, 20) * (1.2 if promo else 1.0) * (1.1 if is_open else 0.5))
                if customers < 0:
                    customers = 0

                row = {
                    "no": store_id,
                    "businessName": business_name,
                    "businessType": business_type,
                    "address": address,
                    "isDigital": is_digital,
                    "hasWebsite": has_website,
                    "promo": promo,
                    "nationalHoliday": national_holiday,
                    "schoolHoliday": school_holiday,
                    "isWeekend": is_weekend,
                    "dayofWeek": dayofweek,
                    "isOpen": is_open,
                    "operatingHour": operating_hour,
                    "date": date_obj.strftime("%Y-%m-%d"),
                    "sales": sales,
                    "customers": customers,
                }
                writer.writerow(row)
            store_id += 1

def forecast_sales_arima(csv_file="synthetic_store_data.csv", forecast_days=30):
    """
    For each store, fit an ARIMA model to the sales time series and forecast future sales.
    Plots the forecast for the first store as an example.
    """
    df = pd.read_csv(csv_file)
    store_ids = df['no'].unique()
    forecasts = {}

    for store_id in store_ids:
        store_df = df[df['no'] == store_id].sort_values('date')
        store_df['date'] = pd.to_datetime(store_df['date'])
        store_df = store_df.set_index('date')
        sales_series = store_df['sales'].asfreq('D').fillna(method='ffill')

        # Fit ARIMA model
        model = auto_arima(sales_series, seasonal=False, suppress_warnings=True, error_action='ignore')
        forecast = model.predict(n_periods=forecast_days)
        forecasts[store_id] = forecast

        # Plot for the first store only
        if store_id == store_ids[0]:
            plt.figure(figsize=(12, 6))
            plt.plot(sales_series, label='Historical Sales')
            future_dates = pd.date_range(sales_series.index[-1] + pd.Timedelta(days=1), periods=forecast_days)
            plt.plot(future_dates, forecast, label='Forecast', linestyle='--')
            plt.title(f'Store {store_id} Sales Forecast')
            plt.xlabel('Date')
            plt.ylabel('Sales')
            plt.legend()
            plt.tight_layout()
            plt.show()

    return forecasts

if __name__ == "__main__":
    generate_dataset_csv()
    forecast_sales_arima() 