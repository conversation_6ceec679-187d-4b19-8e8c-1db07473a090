# Columns for store information and time series analysis
no = int                # Business ID
businessName = str      # Store name
businessType = str      # Type of business
address = str           # Store address (consider extracting city/region for clustering)
isDigital = bool        # Is the store avalaible digital
hasWebsite = bool       # Does the store have a website
promo = bool            # Promotion running on this day
nationalHoliday = bool  # Is this day a national holiday
schoolHoliday = bool    # Is this day a school holiday
isWeekend = bool        # Is this day a weekend
dayofWeek = int         # Day of the week (0=Monday, 6=Sunday)
isOpen = bool           # Is the store open on this day
operatingHour = str     # Operating hours of the store on this day

date = str              # Date (YYYY#MM#DD), required for time series from 2020 to 2025
sales = float           # Sales amount for the day, required for ARIMA/time series
customers = int         # Number of customers on this day, required for ARIMA/time series

# List of business names
def get_business_type(business_name):
    mapping = {
        "F&B": [
            "Rasa Pagi", "Sambal Senja", "Dapur Loka", "Teh & Cerita", "Jajan Jadoel"
        ],
        "Digital & Teknologi": [
            "Jejak Digital", "Koding Karya", "Data Cerah", "BitNusa", "NusaCloud"
        ],
        "Retail & Toko Serba Ada": [
            "Toko PastiAda", "Langgananku", "RumaRaya", "Harga Loka", "Belanja Pintar"
        ],
        "Fashion & Gaya Hidup": [
            "Bumi Perca", "Hijab Pelita", "Langit Busana", "Rancak Raya", "Lini Rupa"
        ],
        "Kreatif & Desain": [
            "Studio Karsa", "Citra Akar", "IdeLokal", "Bentuk Cerita", "Warna Rasa"
        ],
        "Kesehatan & Kecantikan": [
            "Sehat Sentosa", "Rupa Sejiwa", "Laras Care", "Klinik Asa", "CantikNusa"
        ],
        "Pendidikan & Pelatihan": [
            "Langkah Cerah", "Pelita Cita", "Kelas Rakyat", "Asa Cendekia", "Bimbingan Cerita"
        ],
        "Travel & Pariwisata": [
            "Mitra Langit", "Jalan Jelajah", "NusaTrip", "Langkah Loka", "Tamasya Cerah"
        ],
        "Kerajinan & Produk Lokal": [
            "Kriya Kita", "Rupa Nusantara", "Sentra Loka", "Karya Warna", "Loka Bambu"
        ],
        "Properti & Kontraktor": [
            "Rancang Sejahtera", "Lahan Asa", "Rumah Kita", "Bangun Cerita", "Tata Griya"
        ],
    }
    for business_type, names in mapping.items():
        if business_name in names:
            return business_type
    return "Unknown"

business_addresses = {
    "Rasa Pagi": "Jl. Melati No. 12, Jakarta Selatan",
    "Sambal Senja": "Jl. Merdeka No. 45, Bandung",
    "Dapur Loka": "Jl. Kenanga No. 8, Surabaya",
    "Teh & Cerita": "Jl. Diponegoro No. 21, Yogyakarta",
    "Jajan Jadoel": "Jl. Gajah Mada No. 5, Semarang",
    "Jejak Digital": "Jl. Sudirman No. 88, Jakarta Pusat",
    "Koding Karya": "Jl. Setiabudi No. 10, Bandung",
    "Data Cerah": "Jl. Pahlawan No. 3, Surabaya",
    "BitNusa": "Jl. Pemuda No. 17, Malang",
    "NusaCloud": "Jl. Ahmad Yani No. 22, Bekasi",
    "Toko PastiAda": "Jl. Siliwangi No. 9, Bogor",
    "Langgananku": "Jl. Cendrawasih No. 14, Depok",
    "RumaRaya": "Jl. Anggrek No. 7, Tangerang",
    "Harga Loka": "Jl. Rajawali No. 2, Medan",
    "Belanja Pintar": "Jl. Kartini No. 11, Palembang",
    "Bumi Perca": "Jl. Sisingamangaraja No. 6, Pekanbaru",
    "Hijab Pelita": "Jl. Dipatiukur No. 13, Bandung",
    "Langit Busana": "Jl. Slamet Riyadi No. 18, Solo",
    "Rancak Raya": "Jl. Pattimura No. 4, Padang",
    "Lini Rupa": "Jl. Pemuda No. 20, Semarang",
    "Studio Karsa": "Jl. Ganesha No. 1, Bandung",
    "Citra Akar": "Jl. Majapahit No. 15, Surabaya",
    "IdeLokal": "Jl. Sultan Agung No. 19, Jakarta Timur",
    "Bentuk Cerita": "Jl. Diponegoro No. 7, Yogyakarta",
    "Warna Rasa": "Jl. Gatot Subroto No. 23, Denpasar",
    "Sehat Sentosa": "Jl. Ahmad Yani No. 30, Bekasi",
    "Rupa Sejiwa": "Jl. Siliwangi No. 16, Bogor",
    "Laras Care": "Jl. Kenanga No. 2, Surabaya",
    "Klinik Asa": "Jl. Melati No. 8, Jakarta Selatan",
    "CantikNusa": "Jl. Anggrek No. 5, Tangerang",
    "Langkah Cerah": "Jl. Rajawali No. 12, Medan",
    "Pelita Cita": "Jl. Kartini No. 3, Palembang",
    "Kelas Rakyat": "Jl. Sisingamangaraja No. 10, Pekanbaru",
    "Asa Cendekia": "Jl. Dipatiukur No. 2, Bandung",
    "Bimbingan Cerita": "Jl. Slamet Riyadi No. 5, Solo",
    "Mitra Langit": "Jl. Pattimura No. 8, Padang",
    "Jalan Jelajah": "Jl. Pemuda No. 25, Semarang",
    "NusaTrip": "Jl. Ganesha No. 3, Bandung",
    "Langkah Loka": "Jl. Majapahit No. 9, Surabaya",
    "Tamasya Cerah": "Jl. Sultan Agung No. 11, Jakarta Timur",
    "Kriya Kita": "Jl. Diponegoro No. 15, Yogyakarta",
    "Rupa Nusantara": "Jl. Gatot Subroto No. 5, Denpasar",
    "Sentra Loka": "Jl. Ahmad Yani No. 18, Bekasi",
    "Karya Warna": "Jl. Siliwangi No. 7, Bogor",
    "Loka Bambu": "Jl. Cendrawasih No. 10, Depok",
    "Rancang Sejahtera": "Jl. Anggrek No. 12, Tangerang",
    "Lahan Asa": "Jl. Rajawali No. 6, Medan",
    "Rumah Kita": "Jl. Kartini No. 8, Palembang",
    "Bangun Cerita": "Jl. Sisingamangaraja No. 2, Pekanbaru",
    "Tata Griya": "Jl. Dipatiukur No. 6, Bandung",
}

# Dictionary mapping each business name to (isDigital, hasWebsite)
business_digital_info = {
    # F&B
    "Rasa Pagi": (False, False),
    "Sambal Senja": (False, False),
    "Dapur Loka": (False, False),
    "Teh & Cerita": (False, False),
    "Jajan Jadoel": (False, False),
    # Digital & Teknologi
    "Jejak Digital": (True, True),
    "Koding Karya": (True, True),
    "Data Cerah": (True, True),
    "BitNusa": (True, True),
    "NusaCloud": (True, True),
    # Retail & Toko Serba Ada
    "Toko PastiAda": (False, True),  # Has website for minimarket
    "Langgananku": (True, True),     # Digital warung
    "RumaRaya": (False, True),
    "Harga Loka": (False, False),
    "Belanja Pintar": (False, True),
    # Fashion & Gaya Hidup
    "Bumi Perca": (False, True),
    "Hijab Pelita": (False, True),
    "Langit Busana": (False, True),
    "Rancak Raya": (False, True),
    "Lini Rupa": (False, True),
    # Kreatif & Desain
    "Studio Karsa": (True, True),
    "Citra Akar": (True, True),
    "IdeLokal": (True, True),
    "Bentuk Cerita": (True, True),
    "Warna Rasa": (True, True),
    # Kesehatan & Kecantikan
    "Sehat Sentosa": (False, True),
    "Rupa Sejiwa": (False, True),
    "Laras Care": (False, True),
    "Klinik Asa": (False, True),
    "CantikNusa": (False, True),
    # Pendidikan & Pelatihan
    "Langkah Cerah": (True, True),
    "Pelita Cita": (True, True),
    "Kelas Rakyat": (True, True),
    "Asa Cendekia": (True, True),
    "Bimbingan Cerita": (True, True),
    # Travel & Pariwisata
    "Mitra Langit": (True, True),
    "Jalan Jelajah": (True, True),
    "NusaTrip": (True, True),
    "Langkah Loka": (True, True),
    "Tamasya Cerah": (True, True),
    # Kerajinan & Produk Lokal
    "Kriya Kita": (False, True),
    "Rupa Nusantara": (False, True),
    "Sentra Loka": (True, True),
    "Karya Warna": (False, True),
    "Loka Bambu": (False, True),
    # Properti & Kontraktor
    "Rancang Sejahtera": (True, True),
    "Lahan Asa": (False, True),
    "Rumah Kita": (False, True),
    "Bangun Cerita": (False, True),
    "Tata Griya": (True, True),
}

